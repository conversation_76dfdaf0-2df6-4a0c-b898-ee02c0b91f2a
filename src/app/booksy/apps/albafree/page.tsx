"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  MapPin,
  Users,
  Briefcase,
  Star,
  Clock,
  Phone,
  MessageCircle,
  Eye,
  Filter,
  Search,
  ToggleLeft,
  ToggleRight,
  Zap,
  Heart,
  Shield,
  DollarSign,
  Calendar,
  User,
  Settings,
  Bell,
  Plus,
  Navigation,
  RefreshCw
} from "lucide-react";

interface User {
  id: string;
  name: string;
  avatar: string;
  age: number;
  gender: string;
  phone: string;
  email: string;
  location: {
    lat: number;
    lng: number;
    address: string;
    district: string;
    city: string;
  };
  rating: number;
  totalRatings: number;
  isVerified: boolean;
  isOnline: boolean;
  lastSeen: string;
  distance: number;
  role: 'owner' | 'worker';
  profile: {
    description: string;
    skills: string[];
    experience: string;
    availability: string;
    hourlyRate?: number;
    businessType?: string;
    employeeCount?: number;
  };
  stats: {
    completedJobs: number;
    responseTime: string;
    reliability: number;
    noShowRate: number;
  };
  preferences: {
    workTypes: string[];
    maxDistance: number;
    emergencyAvailable: boolean;
    preferredPayment: string[];
  };
}

interface Job {
  id: string;
  title: string;
  description: string;
  category: string;
  urgency: 'low' | 'normal' | 'high' | 'emergency';
  budget: {
    min: number;
    max: number;
    currency: string;
    type: 'hourly' | 'fixed';
  };
  duration: string;
  location: {
    address: string;
    lat: number;
    lng: number;
  };
  requirements: string[];
  postedBy: string;
  postedAt: string;
  startsAt: string;
  status: 'open' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
  applicants: string[];
  assignedTo?: string;
  isEmergency: boolean;
  bbeonjjeokCall?: boolean;
}

// Temporarily using backup page to test layout
export { default } from './backup-page';

function AlbaFreePage() {
  const router = useRouter();
  const [currentRole, setCurrentRole] = useState<'owner' | 'worker'>('worker');
  const [nearbyUsers, setNearbyUsers] = useState<User[]>([]);
  const [activeJobs, setActiveJobs] = useState<Job[]>([]);
  const [currentUser, setCurrentUser] = useState<User | null>({
    id: 'user-001',
    name: 'Người dùng',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
    age: 25,
    gender: 'male',
    phone: '+84-912-345-678',
    email: '<EMAIL>',
    location: {
      lat: 21.0285,
      lng: 105.8542,
      address: 'Hà Nội',
      district: 'Hoàn Kiếm',
      city: 'Hà Nội'
    },
    rating: 4.5,
    totalRatings: 10,
    isVerified: true,
    isOnline: true,
    lastSeen: new Date().toISOString(),
    distance: 0,
    role: 'worker',
    profile: {
      description: 'Người dùng Alba Free',
      skills: ['Quán cà phê', 'Nhà hàng'],
      experience: '1 năm',
      availability: 'Linh hoạt',
      hourlyRate: 35000
    },
    stats: {
      completedJobs: 5,
      responseTime: '5 phút',
      reliability: 95,
      noShowRate: 2
    },
    preferences: {
      workTypes: ['Quán cà phê'],
      maxDistance: 5,
      emergencyAvailable: true,
      preferredPayment: ['Tiền mặt']
    },
    verification: {
      idVerified: true,
      phoneVerified: true,
      emailVerified: true
    },
    workHistory: []
  });
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'distance' | 'rating' | 'price'>('distance');
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [userLocation, setUserLocation] = useState({ lat: 21.0285, lng: 105.8542 }); // Default to Hanoi

  useEffect(() => {
    // Delay initial load to prevent React conflicts
    const timer = setTimeout(() => {
      loadData();
    }, 100);

    return () => clearTimeout(timer);
  }, [currentRole]);

  const loadData = async (forceRefresh = false) => {
    try {
      if (forceRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      // Load current user
      const userResponse = await fetch('/api/booksy/albafree/user/current');
      if (userResponse.ok) {
        const userData = await userResponse.json();
        setCurrentUser(userData);
      }

      // Load nearby users based on role with location
      const nearbyResponse = await fetch(`/api/booksy/albafree/nearby?role=${currentRole === 'owner' ? 'worker' : 'owner'}&lat=${userLocation.lat}&lng=${userLocation.lng}`);
      if (nearbyResponse.ok) {
        const nearbyData = await nearbyResponse.json();
        setNearbyUsers(nearbyData);
      }

      // Load active jobs if user is a worker
      if (currentRole === 'worker') {
        const jobsResponse = await fetch(`/api/booksy/albafree/jobs?status=open&lat=${userLocation.lat}&lng=${userLocation.lng}`);
        if (jobsResponse.ok) {
          const jobsData = await jobsResponse.json();
          setActiveJobs(jobsData);
        }
      }
    } catch (error) {
      console.error('Error loading data:', error);
      // Set some default data to prevent empty state
      setNearbyUsers([]);
      setActiveJobs([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const refreshLocation = async () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          setUserLocation(newLocation);
          loadData(true); // Refresh data with new location
        },
        (error) => {
          console.error('Error getting location:', error);
          // Fallback to manual refresh without location update
          loadData(true);
        }
      );
    } else {
      // Geolocation not supported, just refresh data
      loadData(true);
    }
  };

  const handleRoleSwitch = () => {
    setCurrentRole(currentRole === 'owner' ? 'worker' : 'owner');
  };

  const handleUserClick = (user: User) => {
    router.push(`/booksy/apps/albafree/profile/${user.id}`);
  };

  const handleJobClick = (job: Job) => {
    router.push(`/booksy/apps/albafree/job/${job.id}`);
  };

  const handlePostJob = () => {
    router.push('/booksy/apps/albafree/post-job');
  };

  const handleBbeonjjeokCall = async (userId: string) => {
    try {
      await fetch('/api/booksy/albafree/bbeonjjeok-call', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ targetUserId: userId, senderId: currentUser?.id })
      });
      alert('Cuộc gọi khẩn cấp đã được gửi!');
    } catch (error) {
      console.error('Error sending Bbeonjjeok Call:', error);
    }
  };

  const filteredUsers = nearbyUsers.filter(user => {
    if (searchQuery && !user.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !user.profile.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))) {
      return false;
    }
    if (selectedCategory !== 'all' && !user.profile.skills.includes(selectedCategory)) {
      return false;
    }
    return true;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'distance': return a.distance - b.distance;
      case 'rating': return b.rating - a.rating;
      case 'price': return (a.profile.hourlyRate || 0) - (b.profile.hourlyRate || 0);
      default: return 0;
    }
  });

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'normal': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải Alba Free...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.back()}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Alba Free</h1>
                <p className="text-sm text-gray-500">Kết nối việc làm ngay lập tức</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={refreshLocation}
                disabled={refreshing}
                className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50"
                title="Làm mới vị trí và danh sách"
              >
                <RefreshCw className={`h-5 w-5 text-gray-600 ${refreshing ? 'animate-spin' : ''}`} />
              </button>
              <button className="p-2 hover:bg-gray-100 rounded-full">
                <Bell className="h-5 w-5 text-gray-600" />
              </button>
              <button
                onClick={() => router.push('/booksy/apps/albafree/test-layout')}
                className="p-2 hover:bg-gray-100 rounded-full"
                title="Kiểm tra layout"
              >
                <Settings className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Role Switch */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center justify-center space-x-4">
            <span className={`text-sm font-medium ${currentRole === 'worker' ? 'text-blue-600' : 'text-gray-500'}`}>
              Tìm việc
            </span>
            <button
              onClick={handleRoleSwitch}
              className="flex items-center p-1 bg-gray-100 rounded-full"
            >
              {currentRole === 'worker' ? (
                <ToggleLeft className="h-8 w-8 text-blue-600" />
              ) : (
                <ToggleRight className="h-8 w-8 text-green-600" />
              )}
            </button>
            <span className={`text-sm font-medium ${currentRole === 'owner' ? 'text-green-600' : 'text-gray-500'}`}>
              Tuyển người
            </span>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center space-x-2 mb-3">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder={currentRole === 'worker' ? "Tìm chủ cửa hàng, công việc..." : "Tìm nhân viên, kỹ năng..."}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Filter className="h-4 w-4 text-gray-600" />
            </button>
          </div>

          {showFilters && (
            <div className="flex items-center space-x-2 mb-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'distance' | 'rating' | 'price')}
                className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
              >
                <option value="distance">Khoảng cách</option>
                <option value="rating">Đánh giá</option>
                <option value="price">Giá</option>
              </select>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
              >
                <option value="all">Tất cả</option>
                <option value="restaurant">Nhà hàng</option>
                <option value="cafe">Quán cà phê</option>
                <option value="retail">Bán lẻ</option>
                <option value="delivery">Giao hàng</option>
                <option value="cleaning">Dọn dẹp</option>
                <option value="event">Sự kiện</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Current User Status */}
      {currentUser && (
        <div className="bg-gradient-to-r from-blue-500 to-green-500 text-white">
          <div className="max-w-md mx-auto px-4 py-3">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <img
                  src={currentUser.avatar}
                  alt={currentUser.name}
                  className="w-12 h-12 rounded-full border-2 border-white"
                />
                <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                  currentUser.isOnline ? 'bg-green-500' : 'bg-gray-400'
                }`}></div>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold">{currentUser.name}</h3>
                  {currentUser.isVerified && <Shield className="h-4 w-4 text-yellow-300" />}
                </div>
                <div className="flex items-center space-x-4 text-sm opacity-90">
                  <span className="flex items-center space-x-1">
                    <Star className="h-3 w-3" />
                    <span>{currentUser.rating}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <MapPin className="h-3 w-3" />
                    <span>{currentUser.location.district}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>{currentUser.stats.responseTime}</span>
                  </span>
                </div>
              </div>
              {currentRole === 'owner' && (
                <button
                  onClick={handlePostJob}
                  className="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 flex items-center space-x-1"
                >
                  <Plus className="h-4 w-4" />
                  <span>Đăng việc</span>
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-md mx-auto px-4 py-4">
        {currentRole === 'worker' ? (
          <>
            {/* Emergency Jobs Section */}
            {activeJobs.filter(job => job.isEmergency).length > 0 && (
              <div className="mb-6">
                <div className="flex items-center space-x-2 mb-3">
                  <Zap className="h-5 w-5 text-red-500" />
                  <h2 className="text-lg font-semibold text-gray-900">Việc khẩn cấp</h2>
                  <span className="bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium">
                    Cần ngay
                  </span>
                </div>
                <div className="space-y-3">
                  {activeJobs.filter(job => job.isEmergency).slice(0, 3).map((job) => (
                    <div
                      key={job.id}
                      onClick={() => handleJobClick(job)}
                      className="bg-red-50 border border-red-200 rounded-lg p-4 cursor-pointer hover:bg-red-100 transition-colors"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-gray-900">{job.title}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(job.urgency)}`}>
                          {job.urgency === 'emergency' ? 'Khẩn cấp' : job.urgency}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{job.description}</p>
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-4">
                          <span className="flex items-center space-x-1 text-green-600">
                            <DollarSign className="h-3 w-3" />
                            <span>{job.budget.min.toLocaleString()}-{job.budget.max.toLocaleString()} VNĐ</span>
                          </span>
                          <span className="flex items-center space-x-1 text-gray-500">
                            <Clock className="h-3 w-3" />
                            <span>{job.duration}</span>
                          </span>
                        </div>
                        {job.bbeonjjeokCall && (
                          <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">
                            Gọi khẩn
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Nearby Owners */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-semibold text-gray-900">Chủ cửa hàng gần đây</h2>
                <span className="text-sm text-gray-500">{filteredUsers.length} người</span>
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Nearby Workers */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-semibold text-gray-900">Nhân viên gần đây</h2>
                <span className="text-sm text-gray-500">{filteredUsers.length} người</span>
              </div>
            </div>
          </>
        )}

        {/* User Cards */}
        <div className="space-y-4">
          {filteredUsers.map((user) => (
            <div
              key={user.id}
              onClick={() => handleUserClick(user)}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-3">
                <div className="relative">
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${
                    user.isOnline ? 'bg-green-500' : 'bg-gray-400'
                  }`}></div>
                  {user.isVerified && (
                    <div className="absolute -top-1 -right-1 bg-blue-500 rounded-full p-1">
                      <Shield className="h-3 w-3 text-white" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-gray-900 truncate">{user.name}</h3>
                      <span className="text-sm text-gray-500">({user.age})</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium text-gray-700">{user.rating}</span>
                      <span className="text-xs text-gray-500">({user.totalRatings})</span>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-2 line-clamp-2">{user.profile.description}</p>

                  <div className="flex flex-wrap gap-1 mb-2">
                    {user.profile.skills.slice(0, 3).map((skill, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                    {user.profile.skills.length > 3 && (
                      <span className="text-xs text-gray-500">+{user.profile.skills.length - 3} khác</span>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center space-x-1 text-gray-500">
                        <MapPin className="h-3 w-3" />
                        <span>{user.distance.toFixed(1)}km</span>
                      </span>
                      <span className="flex items-center space-x-1 text-gray-500">
                        <Clock className="h-3 w-3" />
                        <span>{user.stats.responseTime}</span>
                      </span>
                      {user.profile.hourlyRate && (
                        <span className="flex items-center space-x-1 text-green-600">
                          <DollarSign className="h-3 w-3" />
                          <span>{user.profile.hourlyRate.toLocaleString()}đ/h</span>
                        </span>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      {user.preferences.emergencyAvailable && (
                        <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-medium">
                          Khẩn cấp OK
                        </span>
                      )}
                      {currentRole === 'owner' && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleBbeonjjeokCall(user.id);
                          }}
                          className="bg-yellow-500 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-yellow-600 flex items-center space-x-1"
                        >
                          <Zap className="h-3 w-3" />
                          <span>Gọi khẩn</span>
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-100">
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>Hoàn thành: {user.stats.completedJobs}</span>
                      <span>Độ tin cậy: {user.stats.reliability}%</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(`tel:${user.phone}`);
                        }}
                        className="p-1 text-gray-400 hover:text-blue-600"
                      >
                        <Phone className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Open messaging
                        }}
                        className="p-1 text-gray-400 hover:text-green-600"
                      >
                        <MessageCircle className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleUserClick(user);
                        }}
                        className="p-1 text-gray-400 hover:text-purple-600"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {currentRole === 'worker' ? 'Không có chủ cửa hàng nào gần đây' : 'Không có nhân viên nào gần đây'}
            </h3>
            <p className="text-gray-500 mb-4">
              Thử mở rộng bán kính tìm kiếm hoặc thay đổi bộ lọc
            </p>
            <button
              onClick={() => {
                setSearchQuery("");
                setSelectedCategory("all");
                refreshLocation();
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Đặt lại bộ lọc & Làm mới
            </button>
          </div>
        )}
      </div>

      {/* Floating Action Button */}
      {currentRole === 'owner' && (
        <button
          onClick={handlePostJob}
          className="fixed bottom-6 right-6 bg-green-600 text-white p-4 rounded-full shadow-lg hover:bg-green-700 transition-colors"
        >
          <Plus className="h-6 w-6" />
        </button>
      )}
    </div>
  );
}
