"use client";

import { useState } from "react";
import { RefreshCw, Users, Briefcase, Star, MapPin, Clock, Zap } from "lucide-react";

export default function AlbaFreeBackupPage() {
  const [currentRole, setCurrentRole] = useState<'worker' | 'owner'>('worker');
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1000);
  };

  const handleRoleSwitch = () => {
    setCurrentRole(prev => prev === 'worker' ? 'owner' : 'worker');
  };

  // Mock data
  const mockUsers = [
    {
      id: 'user-001',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      rating: 4.8,
      distance: 0.5,
      isOnline: true,
      skills: ['<PERSON>u<PERSON> cà phê', '<PERSON><PERSON><PERSON> v<PERSON>'],
      location: '<PERSON><PERSON><PERSON>, <PERSON><PERSON>'
    },
    {
      id: 'user-002',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      rating: 4.9,
      distance: 1.2,
      isOnline: false,
      skills: ['Tiệm bánh', 'Tiếng Anh'],
      location: 'Hoàn Kiếm, Hà Nội'
    }
  ];

  const mockJobs = [
    {
      id: 'job-001',
      title: 'Cần gấp nhân viên phục vụ quán cà phê!',
      description: 'Cần người phục vụ từ 2h chiều đến 6h chiều hôm nay',
      budget: { min: 35000, max: 45000 },
      duration: '4 giờ',
      isEmergency: true,
      bbeonjjeokCall: true
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold text-gray-900">Alba Free</h1>
              <p className="text-sm text-gray-500">Kết nối việc làm ngay lập tức</p>
            </div>
            <button 
              onClick={handleRefresh}
              disabled={refreshing}
              className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50"
              title="Làm mới vị trí và danh sách"
            >
              <RefreshCw className={`h-5 w-5 text-gray-600 ${refreshing ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {/* Role Toggle */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-center space-x-4">
            <span className={`text-sm font-medium ${currentRole === 'worker' ? 'text-blue-600' : 'text-gray-500'}`}>
              Tìm việc
            </span>
            <button
              onClick={handleRoleSwitch}
              className="flex items-center p-1 bg-gray-100 rounded-full"
            >
              <div className={`w-8 h-8 rounded-full transition-colors ${
                currentRole === 'worker' ? 'bg-blue-600' : 'bg-green-600'
              }`}></div>
            </button>
            <span className={`text-sm font-medium ${currentRole === 'owner' ? 'text-green-600' : 'text-gray-500'}`}>
              Tuyển người
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-md mx-auto px-4 py-6">
        {currentRole === 'worker' && (
          <>
            {/* Emergency Jobs */}
            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-3">
                <Zap className="h-5 w-5 text-red-500" />
                <h2 className="text-lg font-semibold text-gray-900">Việc khẩn cấp</h2>
                <span className="bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium">
                  Cần ngay
                </span>
              </div>
              
              {mockJobs.map((job) => (
                <div key={job.id} className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-medium">
                      Rất gấp
                    </span>
                    {job.bbeonjjeokCall && (
                      <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">
                        Gọi khẩn
                      </span>
                    )}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{job.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{job.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-green-600 font-medium">
                      {job.budget.min.toLocaleString()}-{job.budget.max.toLocaleString()}đ/giờ
                    </span>
                    <span className="text-sm text-gray-500">{job.duration}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* Nearby Owners */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-semibold text-gray-900">Chủ cửa hàng gần đây</h2>
                <span className="text-sm text-gray-500">{mockUsers.length} người</span>
              </div>
            </div>
          </>
        )}

        {currentRole === 'owner' && (
          <>
            {/* Nearby Workers */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-semibold text-gray-900">Nhân viên gần đây</h2>
                <span className="text-sm text-gray-500">{mockUsers.length} người</span>
              </div>
            </div>
          </>
        )}

        {/* User Cards */}
        <div className="space-y-4">
          {mockUsers.map((user) => (
            <div key={user.id} className="bg-white rounded-lg shadow-sm border p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start space-x-3">
                <div className="relative">
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                    user.isOnline ? 'bg-green-500' : 'bg-gray-400'
                  }`}></div>
                </div>

                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-semibold text-gray-900">{user.name}</h3>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">{user.rating}</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4 text-xs text-gray-500 mb-2">
                    <span className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span>{user.location}</span>
                    </span>
                    <span>{user.distance}km</span>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-2">
                    {user.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <span className={`text-xs px-2 py-1 rounded ${
                      user.isOnline ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                    }`}>
                      {user.isOnline ? 'Online' : 'Offline'}
                    </span>
                    
                    {currentRole === 'owner' && (
                      <button className="bg-yellow-500 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-yellow-600 flex items-center space-x-1">
                        <Zap className="h-3 w-3" />
                        <span>Gọi khẩn</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Test Status */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-green-900 mb-2">
            ✅ Alba Free đang hoạt động
          </h3>
          <ul className="text-xs text-green-800 space-y-1">
            <li>• Layout độc lập (không có header/sidebar Booksy)</li>
            <li>• Giao diện tiếng Việt</li>
            <li>• Tính năng làm mới</li>
            <li>• Chuyển đổi vai trò</li>
            <li>• Responsive design</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
