"use client";

import { useState, useEffect } from "react";
import { RefreshCw, Users, Briefcase, Star, MapPin, Clock, Zap, Filter, Search, MessageCircle } from "lucide-react";

interface User {
  id: string;
  name: string;
  avatar: string;
  rating: number;
  distance: number;
  isOnline: boolean;
  skills: string[];
  location: string;
  role: 'worker' | 'owner';
}

interface Job {
  id: string;
  title: string;
  description: string;
  budget: { min: number; max: number };
  duration: string;
  isEmergency: boolean;
  bbeonjjeokCall: boolean;
  location: { address: string; lat: number; lng: number };
  distance?: number;
}

export default function AlbaFreeBackupPage() {
  const [currentRole, setCurrentRole] = useState<'worker' | 'owner'>('worker');
  const [refreshing, setRefreshing] = useState(false);
  const [radiusFilter, setRadiusFilter] = useState<number | 'all'>(10);
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [users, setUsers] = useState<User[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [userLocation, setUserLocation] = useState({ lat: 21.0285, lng: 105.8542 }); // Default to Hanoi

  useEffect(() => {
    loadData();
  }, [currentRole, radiusFilter]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load nearby users based on role and radius
      const targetRole = currentRole === 'worker' ? 'owner' : 'worker';
      const radius = radiusFilter === 'all' ? 50 : radiusFilter; // Use large radius for "all"

      try {
        const usersResponse = await fetch(
          `/api/booksy/albafree/nearby?role=${targetRole}&lat=${userLocation.lat}&lng=${userLocation.lng}&radius=${radius}`
        );

        if (usersResponse.ok) {
          const usersData = await usersResponse.json();
          // Ensure each user has the required properties
          const normalizedUsers = usersData.map((user: any) => ({
            ...user,
            skills: user.skills || user.profile?.skills || [],
            name: user.name || 'Unknown User',
            distance: user.distance || 0
          }));
          setUsers(normalizedUsers);
        } else {
          throw new Error('Failed to fetch users');
        }
      } catch (userError) {
        console.error('Error loading users:', userError);
        setUsers(mockUsers);
      }

      // Load jobs if user is a worker
      if (currentRole === 'worker') {
        try {
          const jobsResponse = await fetch(
            `/api/booksy/albafree/jobs?status=open&lat=${userLocation.lat}&lng=${userLocation.lng}&radius=${radius}&emergency=true`
          );

          if (jobsResponse.ok) {
            const jobsData = await jobsResponse.json();
            setJobs(jobsData);
          } else {
            throw new Error('Failed to fetch jobs');
          }
        } catch (jobError) {
          console.error('Error loading jobs:', jobError);
          setJobs(mockJobs);
        }
      }
    } catch (error) {
      console.error('Error loading data:', error);
      // Fallback to mock data
      setUsers(mockUsers);
      setJobs(mockJobs);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadData().finally(() => setRefreshing(false));
  };

  const handleRoleSwitch = () => {
    setCurrentRole(prev => prev === 'worker' ? 'owner' : 'worker');
  };

  // Mock data
  const mockUsers = [
    {
      id: 'user-001',
      name: 'Nguyễn Minh Tuấn',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      rating: 4.8,
      distance: 0.5,
      isOnline: true,
      skills: ['Quán cà phê', 'Phục vụ'],
      location: 'Đống Đa, Hà Nội'
    },
    {
      id: 'user-002',
      name: 'Trần Thị Lan',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      rating: 4.9,
      distance: 1.2,
      isOnline: false,
      skills: ['Tiệm bánh', 'Tiếng Anh'],
      location: 'Hoàn Kiếm, Hà Nội'
    }
  ];

  const mockJobs = [
    {
      id: 'job-001',
      title: 'Cần gấp nhân viên phục vụ quán cà phê!',
      description: 'Cần người phục vụ từ 2h chiều đến 6h chiều hôm nay',
      budget: { min: 35000, max: 45000 },
      duration: '4 giờ',
      isEmergency: true,
      bbeonjjeokCall: true,
      location: 'Đống Đa, Hà Nội',
      distance: 0.8
    }
  ];

  const handleJobClick = (jobId: string) => {
    // Navigate to job detail page
    window.location.href = `/booksy/apps/albafree/job/${jobId}`;
  };

  const handleUserClick = (userId: string) => {
    // Navigate to user profile page
    window.location.href = `/booksy/apps/albafree/profile/${userId}`;
  };

  const handleChatClick = (userId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    // Navigate to chat page
    window.location.href = `/booksy/apps/albafree/chat/${userId}`;
  };

  // Filter users based on search query and radius
  const filteredUsers = users.filter(user => {
    // Search filter
    if (searchQuery) {
      const nameMatch = user.name?.toLowerCase().includes(searchQuery.toLowerCase());
      const skills = user.skills || user.profile?.skills || [];
      const skillMatch = skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));

      if (!nameMatch && !skillMatch) {
        return false;
      }
    }

    // Radius filter
    if (radiusFilter !== 'all' && user.distance && user.distance > radiusFilter) {
      return false;
    }

    return true;
  });

  // Filter jobs based on search query and radius
  const filteredJobs = jobs.filter(job => {
    // Search filter
    if (searchQuery && !job.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !job.description.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }

    // Radius filter
    if (radiusFilter !== 'all' && job.distance && job.distance > radiusFilter) {
      return false;
    }

    return true;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải Alba Free...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold text-gray-900">Alba Free</h1>
              <p className="text-sm text-gray-500">Kết nối việc làm ngay lập tức</p>
            </div>
            <button 
              onClick={handleRefresh}
              disabled={refreshing}
              className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50"
              title="Làm mới vị trí và danh sách"
            >
              <RefreshCw className={`h-5 w-5 text-gray-600 ${refreshing ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {/* Role Toggle */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-center space-x-4">
            <span className={`text-sm font-medium ${currentRole === 'worker' ? 'text-blue-600' : 'text-gray-500'}`}>
              Tìm việc
            </span>
            <button
              onClick={handleRoleSwitch}
              className="flex items-center p-1 bg-gray-100 rounded-full"
              aria-label="Chuyển đổi vai trò"
            >
              <div className={`w-8 h-8 rounded-full transition-colors ${
                currentRole === 'worker' ? 'bg-blue-600' : 'bg-green-600'
              }`}></div>
            </button>
            <span className={`text-sm font-medium ${currentRole === 'owner' ? 'text-green-600' : 'text-gray-500'}`}>
              Tuyển người
            </span>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4 py-3">
          <div className="flex items-center space-x-2 mb-3">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder={currentRole === 'worker' ? "Tìm chủ cửa hàng, công việc..." : "Tìm nhân viên, kỹ năng..."}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Filter className="h-4 w-4 text-gray-600" />
            </button>
          </div>

          {showFilters && (
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bán kính tìm kiếm
                </label>
                <select
                  value={radiusFilter}
                  onChange={(e) => setRadiusFilter(e.target.value === 'all' ? 'all' : parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={2}>2 km</option>
                  <option value={5}>5 km</option>
                  <option value={10}>10 km</option>
                  <option value={20}>20 km</option>
                  <option value="all">Không giới hạn (việc online)</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="max-w-md mx-auto px-4 py-6">
        {currentRole === 'worker' && (
          <>
            {/* Emergency Jobs */}
            <div className="mb-6">
              <div className="flex items-center space-x-2 mb-3">
                <Zap className="h-5 w-5 text-red-500" />
                <h2 className="text-lg font-semibold text-gray-900">Việc khẩn cấp</h2>
                <span className="bg-red-100 text-red-600 px-2 py-1 rounded-full text-xs font-medium">
                  Cần ngay
                </span>
              </div>
              
              {filteredJobs.map((job) => (
                <div
                  key={job.id}
                  onClick={() => handleJobClick(job.id)}
                  className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4 cursor-pointer hover:bg-red-100 transition-colors"
                >
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-medium">
                      Rất gấp
                    </span>
                    {job.bbeonjjeokCall && (
                      <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">
                        Gọi khẩn
                      </span>
                    )}
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">{job.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{job.description}</p>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-green-600 font-medium">
                      {job.budget.min.toLocaleString()}-{job.budget.max.toLocaleString()}đ/giờ
                    </span>
                    <span className="text-sm text-gray-500">{job.duration}</span>
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span>{job.location.address}</span>
                    </span>
                    <span>{job.distance ? `${job.distance.toFixed(1)}km` : 'Gần đây'}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* Nearby Owners */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-semibold text-gray-900">Chủ cửa hàng gần đây</h2>
                <span className="text-sm text-gray-500">{filteredUsers.length} người</span>
              </div>
            </div>
          </>
        )}

        {currentRole === 'owner' && (
          <>
            {/* Nearby Workers */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-3">
                <h2 className="text-lg font-semibold text-gray-900">Nhân viên gần đây</h2>
                <span className="text-sm text-gray-500">{filteredUsers.length} người</span>
              </div>
            </div>
          </>
        )}

        {/* User Cards */}
        <div className="space-y-4">
          {filteredUsers.map((user) => (
            <div
              key={user.id}
              onClick={() => handleUserClick(user.id)}
              className="bg-white rounded-lg shadow-sm border p-4 hover:shadow-md transition-shadow cursor-pointer"
            >
              <div className="flex items-start space-x-3">
                <div className="relative">
                  <img
                    src={user.avatar || 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'}
                    alt={user.name || 'User'}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                    user.isOnline ? 'bg-green-500' : 'bg-gray-400'
                  }`}></div>
                </div>

                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className="font-semibold text-gray-900">{user.name || 'Unknown User'}</h3>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">{user.rating || 0}</span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4 text-xs text-gray-500 mb-2">
                    <span className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span>{user.location?.district || user.location?.address || user.location || 'Không rõ'}</span>
                    </span>
                    <span>{user.distance ? `${user.distance.toFixed(1)}km` : 'Gần đây'}</span>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-2">
                    {(user.skills || user.profile?.skills || []).map((skill, index) => (
                      <span
                        key={index}
                        className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <span className={`text-xs px-2 py-1 rounded ${
                      user.isOnline ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                    }`}>
                      {user.isOnline ? 'Online' : 'Offline'}
                    </span>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => handleChatClick(user.id, e)}
                        className="bg-green-500 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-green-600 flex items-center space-x-1"
                      >
                        <MessageCircle className="h-3 w-3" />
                        <span>Chat</span>
                      </button>

                      {currentRole === 'owner' && (
                        <button className="bg-yellow-500 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-yellow-600 flex items-center space-x-1">
                          <Zap className="h-3 w-3" />
                          <span>Gọi khẩn</span>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {currentRole === 'worker' ? 'Không có chủ cửa hàng nào gần đây' : 'Không có nhân viên nào gần đây'}
            </h3>
            <p className="text-gray-500 mb-4">
              {radiusFilter !== 'all'
                ? `Thử mở rộng bán kính tìm kiếm (hiện tại: ${radiusFilter}km)`
                : 'Thử thay đổi từ khóa tìm kiếm hoặc làm mới dữ liệu'
              }
            </p>
            <div className="space-y-2">
              {radiusFilter !== 'all' && (
                <button
                  onClick={() => setRadiusFilter('all')}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 mr-2"
                >
                  Mở rộng tìm kiếm
                </button>
              )}
              <button
                onClick={() => {
                  setSearchQuery("");
                  setRadiusFilter(10);
                  handleRefresh();
                }}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
              >
                Đặt lại & Làm mới
              </button>
            </div>
          </div>
        )}

        {/* Test Status */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-green-900 mb-2">
            ✅ Alba Free với bộ lọc bán kính & chat
          </h3>
          <ul className="text-xs text-green-800 space-y-1">
            <li>• Bộ lọc bán kính: 2km, 5km, 10km, 20km, không giới hạn</li>
            <li>• Tìm kiếm theo tên và kỹ năng</li>
            <li>• Click vào job để xem chi tiết</li>
            <li>• Click vào user để xem profile</li>
            <li>• Nút Chat để trò chuyện trực tiếp</li>
            <li>• API thực tế với dữ liệu JSON</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
